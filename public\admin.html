<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Admin Access - Tutors Alliance Scotland</title>
    <link rel="stylesheet" href="/styles2.css">
    <style>
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 50vh;
            font-size: 1.2em;
            color: #666;
            text-align: center;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto 20px;
        }
    </style>
</head>
<body>
    <!-- ─────────────── HEADER/BANNER ─────────────── -->
    <header>
        <h1>Tutors Alliance Scotland</h1>
        <div class="header-links">
            <a class="banner-login-link login-box" href="/">Home</a>
            <a class="banner-login-link login-box" href="/login.html?role=admin">Login</a>
        </div>
    </header>

    <!-- ─────────────── MAIN CONTENT ─────────────── -->
    <!-- 🔒 SECURITY: Minimal loading page - sensitive content served only after server-side auth -->
    <div class="loading-container">
        <div>
            <div class="spinner"></div>
            <p>Redirecting to secure admin portal...</p>
            <p><small>If you are not redirected automatically, <a href="/admin">click here</a>.</small></p>
        </div>
    </div>

    <script>
        // 🔒 SECURITY: Immediate redirect to server-side authenticated endpoint
        // This ensures no sensitive admin content is ever served to unauthenticated users
        window.location.href = '/admin';
    </script>
</body>
</html>
