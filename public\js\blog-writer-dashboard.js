/**
 * Blog Writer Dashboard - Complete functionality restoration from pre-bf1328c
 * Handles tab switching, form management, blog CRUD operations, and image uploads
 */

import { uploadImage } from '/js/upload-helper.js';

// Global variables
let allBlogs = [];
let handlersInitialized = false;

// DOM element references
let createTabBtn, manageTabBtn, newBlogSection, manageBlogSection;
let blogForm, formHeading, submitBtn, cancelEditBtn, currentImagePreview, removeImageCheckbox;
let titleField, authorField, slugField, categoryField, statusField, excerptField;
let metaDescriptionField, focusKeywordField, tagsField, publishDateField;
let contentField, imageField, blogMicrodata;

/**
 * Initialize the blog writer dashboard
 */
function initBlogWriterDashboard() {
    console.log('[Blog Writer] Initializing dashboard...');
    
    // Get DOM elements
    initDOMElements();
    
    // Initialize functionality
    initTabSwitching();
    initFormHandlers();
    initCharacterCounters();
    initSlugGeneration();
    initBlogListHandlers();
    
    console.log('[Blog Writer] Dashboard initialized successfully');
}

/**
 * Initialize DOM element references
 */
function initDOMElements() {
    // Tab elements
    createTabBtn = document.getElementById('createTabBtn');
    manageTabBtn = document.getElementById('manageTabBtn');
    newBlogSection = document.getElementById('newBlogSection');
    manageBlogSection = document.getElementById('manageBlogSection');
    
    // Form elements
    blogForm = document.getElementById('blogForm');
    formHeading = document.getElementById('formHeading');
    submitBtn = document.getElementById('submitBtn');
    cancelEditBtn = document.getElementById('cancelEditBtn');
    currentImagePreview = document.getElementById('currentImagePreview');
    removeImageCheckbox = document.getElementById('removeImageCheckbox');
    blogMicrodata = document.getElementById('blogMicrodata');
    
    // Form field references
    titleField = document.getElementById('titleField');
    authorField = document.getElementById('authorField');
    slugField = document.getElementById('slugField');
    categoryField = document.getElementById('categoryField');
    statusField = document.getElementById('statusField');
    excerptField = document.getElementById('excerptField');
    metaDescriptionField = document.getElementById('metaDescriptionField');
    focusKeywordField = document.getElementById('focusKeywordField');
    tagsField = document.getElementById('tagsField');

    publishDateField = document.getElementById('publishDateField');
    contentField = document.getElementById('contentField');
    imageField = document.getElementById('imageField');
}

/**
 * Initialize tab switching functionality
 */
function initTabSwitching() {
    createTabBtn.addEventListener('click', () => {
        createTabBtn.classList.add('active');
        manageTabBtn.classList.remove('active');
        newBlogSection.classList.add('active');
        manageBlogSection.classList.remove('active');
        
        // Only reset form when switching to create tab if we're not in edit mode
        if (!blogForm.dataset.editId) {
            resetBlogForm();
        }
    });
    
    manageTabBtn.addEventListener('click', () => {
        manageTabBtn.classList.add('active');
        createTabBtn.classList.remove('active');
        manageBlogSection.classList.add('active');
        newBlogSection.classList.remove('active');
        loadBlogs(); // Load blogs when tab is clicked
    });
}

/**
 * Initialize character counters
 */
function initCharacterCounters() {
    function updateCharCounter(field, maxLength) {
        const counter = field.parentElement.querySelector('.char-counter');
        if (counter) {
            const currentLength = field.value.length;
            counter.textContent = `${currentLength}/${maxLength} characters`;
            counter.style.color = currentLength > maxLength ? '#dc3545' : '#666';
        }
    }
    
    // Add character counting for excerpt and meta description
    excerptField.addEventListener('input', () => updateCharCounter(excerptField, 200));
    metaDescriptionField.addEventListener('input', () => updateCharCounter(metaDescriptionField, 160));
}

/**
 * Initialize slug auto-generation
 */
function initSlugGeneration() {
    // Auto-generate slug from title
    titleField.addEventListener('input', () => {
        if (!slugField.value || slugField.dataset.autoGenerated === 'true') {
            const slug = titleField.value
                .toLowerCase()
                .replace(/[^a-z0-9\s-]/g, '')
                .replace(/\s+/g, '-')
                .replace(/-+/g, '-')
                .replace(/^-|-$/g, '');
            slugField.value = slug;
            slugField.dataset.autoGenerated = 'true';
        }
    });
    
    // Mark slug as manually edited when user types in it
    slugField.addEventListener('input', () => {
        slugField.dataset.autoGenerated = 'false';
    });
}

/**
 * Initialize form handlers
 */
function initFormHandlers() {
    // Prevent duplicate handler initialization
    if (handlersInitialized) {
        console.log('[Blog Writer] Form handlers already initialized, skipping');
        return;
    }

    // Cancel edit button
    cancelEditBtn.addEventListener('click', () => {
        resetBlogForm();
    });

    // Form submission
    blogForm.addEventListener('submit', handleFormSubmission);

    handlersInitialized = true;
    console.log('[Blog Writer] Form handlers initialized');
}

/**
 * Initialize blog list handlers
 */
function initBlogListHandlers() {
    // Handle edit and delete actions
    document.getElementById('blogListBody').addEventListener('click', handleBlogListActions);
    
    // Filter blogs by category
    document.getElementById('blogCategoryFilter').addEventListener('change', (e) => {
        loadBlogs(e.target.value);
    });
    
    // Refresh blogs button
    document.getElementById('refreshBlogsBtn').addEventListener('click', () => {
        loadBlogs(document.getElementById('blogCategoryFilter').value);
    });
    
    // Migration button
    document.getElementById('migrateBlogsBtn').addEventListener('click', handleBlogMigration);
}

/**
 * Reset form to create mode
 */
function resetBlogForm() {
    blogForm.reset();
    delete blogForm.dataset.editId;
    formHeading.textContent = 'Create a New Blog Post';
    submitBtn.textContent = 'Create Blog';
    cancelEditBtn.style.display = 'none';
    currentImagePreview.style.display = 'none';
    removeImageCheckbox.checked = false;
    blogMicrodata.textContent = '';
    
    // Reset character counters
    updateCharCounter(excerptField, 200);
    updateCharCounter(metaDescriptionField, 160);
    
    // Reset slug auto-generation flag
    slugField.dataset.autoGenerated = 'true';
}

/**
 * Update character counter display
 */
function updateCharCounter(field, maxLength) {
    const counter = field.parentElement.querySelector('.char-counter');
    if (counter) {
        const currentLength = field.value.length;
        counter.textContent = `${currentLength}/${maxLength} characters`;
        counter.style.color = currentLength > maxLength ? '#dc3545' : '#666';
    }
}

/**
 * Populate form for editing a blog
 */
function populateBlogForm(blog) {
    console.log('[Blog Writer] Populating form with blog data:', blog);

    // Basic Information fields
    titleField.value = blog.title || '';
    authorField.value = blog.author || '';

    // Generate slug if it doesn't exist (backward compatibility)
    if (blog.slug) {
        slugField.value = blog.slug;
        slugField.dataset.autoGenerated = 'false';
    } else {
        // Auto-generate slug from title for older blogs
        const generatedSlug = blog.title
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
        slugField.value = generatedSlug;
        slugField.dataset.autoGenerated = 'true';
    }

    // Content fields
    excerptField.value = blog.excerpt || '';
    contentField.value = blog.content || '';

    // SEO & Metadata fields (with backward compatibility)
    if (blog.metaDescription !== undefined && blog.metaDescription !== null) {
        metaDescriptionField.value = blog.metaDescription;
    } else {
        // Generate from excerpt or content for older blogs
        const fallbackMeta = blog.excerpt || blog.content.replace(/<[^>]*>/g, '').substring(0, 160);
        metaDescriptionField.value = fallbackMeta.length > 160 ? fallbackMeta.substring(0, 160) + '...' : fallbackMeta;
    }

    focusKeywordField.value = blog.focusKeyword || '';

    // Handle tags - ensure proper format
    if (blog.tags !== undefined && blog.tags !== null) {
        if (Array.isArray(blog.tags)) {
            tagsField.value = blog.tags.join(', ');
        } else if (typeof blog.tags === 'string') {
            tagsField.value = blog.tags;
        } else {
            tagsField.value = '';
        }
    } else {
        tagsField.value = '';
    }

    // Publishing Options
    statusField.value = blog.status || 'published';

    // Update character counters after populating fields
    updateCharCounter(excerptField, 200);
    updateCharCounter(metaDescriptionField, 160);

    // Set category (handle both old and new blog formats)
    if (blog.category && Array.isArray(blog.category)) {
        if (blog.category.includes('parent') && blog.category.includes('tutor')) {
            categoryField.value = 'general';
        } else if (blog.category.includes('parent')) {
            categoryField.value = 'parent';
        } else if (blog.category.includes('tutor')) {
            categoryField.value = 'tutor';
        } else {
            categoryField.value = 'general';
        }
    } else {
        // Fallback for older blog format or missing category
        categoryField.value = 'general';
    }

    // Set publish date
    if (blog.publishDate) {
        const date = new Date(blog.publishDate);
        publishDateField.value = date.toISOString().slice(0, 16);
    } else {
        publishDateField.value = '';
    }

    // Show current image if exists
    if (blog.imagePath) {
        currentImagePreview.style.display = 'block';
        currentImagePreview.querySelector('img').src = blog.imagePath;
        removeImageCheckbox.checked = false;
    } else {
        currentImagePreview.style.display = 'none';
        removeImageCheckbox.checked = false;
    }

    // Update UI for edit mode
    formHeading.textContent = 'Edit Blog Post';
    submitBtn.textContent = 'Update Blog';
    cancelEditBtn.style.display = 'inline-block';
    blogForm.dataset.editId = blog._id;

    console.log('[Blog Writer] Blog form populated for editing:', {
        title: blog.title,
        slug: blog.slug,
        status: blog.status,
        featured: blog.featured,
        tags: blog.tags,
        metaDescription: blog.metaDescription
    });
}

/**
 * Load blogs from the server
 */
async function loadBlogs(category = 'all') {
    const blogListBody = document.getElementById('blogListBody');
    blogListBody.innerHTML = '<tr><td colspan="5" class="loading-message">Loading blog posts...</td></tr>';

    try {
        const response = await fetch('/api/blog-writer', {
            credentials: 'include', // Include cookies for authentication
            headers: {
                'Accept': 'application/json' // Explicitly request JSON
            }
        });

        if (!response.ok) {
            throw new Error(`Failed to fetch blogs: ${response.status} ${response.statusText}`);
        }

        const blogs = await response.json();
        allBlogs = blogs; // Store for editing

        // Filter blogs by category if needed
        let filteredBlogs = blogs;
        if (category !== 'all') {
            if (category === 'general') {
                // General means both parent and tutor categories
                filteredBlogs = blogs.filter(blog =>
                    blog.category.includes('parent') && blog.category.includes('tutor')
                );
            } else {
                // Filter by specific category
                filteredBlogs = blogs.filter(blog => blog.category.includes(category));
            }
        }

        // Clear loading message
        blogListBody.innerHTML = '';

        if (filteredBlogs.length === 0) {
            blogListBody.innerHTML = '<tr><td colspan="5" class="loading-message">No blog posts found</td></tr>';
            return;
        }

        // Add each blog to the table
        filteredBlogs.forEach(blog => {
            const row = document.createElement('tr');

            // Format date
            const date = new Date(blog.publishDate || blog.createdAt);
            const formattedDate = date.toLocaleDateString('en-GB', {
                day: '2-digit',
                month: 'short',
                year: 'numeric'
            });

            // Format category
            let categoryText = blog.category.join(', ');
            if (blog.category.includes('parent') && blog.category.includes('tutor')) {
                categoryText = 'General';
            }

            row.innerHTML = `
                <td>${blog.title}</td>
                <td>${blog.author}</td>
                <td>${categoryText}</td>
                <td>${formattedDate}</td>
                <td>
                    <button class="action-btn edit-btn" data-id="${blog._id}" title="Edit">✏️</button>
                    <button class="action-btn delete-btn" data-id="${blog._id}" title="Delete">🗑️</button>
                </td>
            `;

            blogListBody.appendChild(row);
        });
    } catch (error) {
        console.error('[Blog Writer] Error loading blogs:', error);
        blogListBody.innerHTML = `<tr><td colspan="5" class="loading-message">Error: ${error.message}</td></tr>`;
    }
}

/**
 * Handle blog list actions (edit/delete)
 */
async function handleBlogListActions(e) {
    const blogId = e.target.dataset.id;

    if (e.target.classList.contains('edit-btn')) {
        // Find the blog to edit
        const blog = allBlogs.find(b => b._id === blogId);
        if (!blog) {
            alert('Blog not found for editing');
            return;
        }

        console.log('[Blog Writer] Editing blog:', blog);

        // Set form to edit mode BEFORE switching tabs
        blogForm.dataset.editId = blogId;
        submitBtn.textContent = 'Update Blog Post';

        // Populate form and switch to create tab
        populateBlogForm(blog);
        createTabBtn.click();
        blogForm.scrollIntoView({ behavior: 'smooth' });
    }
    else if (e.target.classList.contains('delete-btn')) {
        if (confirm('Are you sure you want to delete this blog post? This action cannot be undone.')) {
            try {
                const response = await fetch(`/api/blog-writer?id=${blogId}`, {
                    method: 'DELETE',
                    credentials: 'include' // Include cookies for authentication
                });

                if (!response.ok) {
                    throw new Error(`Failed to delete blog: ${response.status} ${response.statusText}`);
                }

                alert('Blog post deleted successfully');
                loadBlogs(document.getElementById('blogCategoryFilter').value); // Reload the list
            } catch (error) {
                console.error('[Blog Writer] Error deleting blog:', error);
                alert(`Error deleting blog: ${error.message}`);
            }
        }
    }
}

/**
 * Handle form submission for creating/updating blogs
 */
async function handleFormSubmission(e) {
    e.preventDefault();

    // Prevent double submission
    if (submitBtn.disabled) {
        console.log('[Blog Writer] Form submission already in progress, ignoring duplicate request');
        return;
    }

    const isEditing = !!blogForm.dataset.editId;
    const blogId = blogForm.dataset.editId;

    const title = titleField.value.trim();
    const author = authorField.value.trim();
    const slug = slugField.value.trim();
    const category = categoryField.value;
    const status = statusField.value;
    const excerpt = excerptField.value.trim();
    const metaDescription = metaDescriptionField.value.trim();
    const focusKeyword = focusKeywordField.value.trim();
    const tags = tagsField.value.trim();

    const publishDate = publishDateField.value;
    const content = contentField.value.trim();

    // Validate required fields
    if (!title || !content || !excerpt) {
        alert('Title, content, and excerpt are required');
        return;
    }

    // Disable submit button and show loading state
    submitBtn.disabled = true;
    const originalButtonText = submitBtn.textContent;
    submitBtn.textContent = isEditing ? 'Updating...' : 'Creating...';

    /* Handle image upload */
    let uploadedImagePath = '';
    if (imageField.files[0]) {
        try {
            uploadedImagePath = await uploadImage(imageField.files[0], 'blog');
        }
        catch (err) {
            // Re-enable submit button on error
            submitBtn.disabled = false;
            submitBtn.textContent = originalButtonText;
            return alert('Upload failed: ' + err.message);
        }
    }

    /* Determine method and URL based on mode */
    const method = isEditing ? 'PUT' : 'POST';
    const url = isEditing ? `/api/blog-writer?id=${blogId}` : '/api/blog-writer';

    /* Build payload */
    const payload = {
        title,
        author: author || 'Tutors Alliance Scotland',
        slug,
        category,
        status,
        excerpt,
        metaDescription,
        focusKeyword,
        tags: tags ? tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [],
        publishDate,
        content,
        imagePath: uploadedImagePath || '',
        removeImage: removeImageCheckbox.checked
    };

    // For POST fallback, add editId to payload
    if (isEditing) {
        payload.editId = blogId;
    }

    /* JSON LD for SEO (only for new blogs) */
    if (!isEditing) {
        const nowISO = new Date().toISOString();
        blogMicrodata.textContent = JSON.stringify({
            "@context": "https://schema.org",
            "@type": "BlogPosting",
            "mainEntityOfPage": { "@id": "https://tutorsalliancescotland.co.uk/blog", "@type": "WebPage" },
            headline: title,
            author: { "@type": "Person", name: author || "Tutors Alliance Scotland" },
            publisher: {
                "@type": "Organization",
                name: "Tutors Alliance Scotland",
                logo: { "@type": "ImageObject", url: "https://tutorsalliancescotland.co.uk/images/bannerShield2.png" }
            },
            datePublished: publishDate ? new Date(publishDate).toISOString() : nowISO,
            dateModified: nowISO,
            description: excerpt || (content.slice(0, 160) + '…'),
            image: uploadedImagePath || "https://tutorsalliancescotland.co.uk/images/defaultBlog.png"
        }, null, 2);
    }

    try {
        /* Send to server */
        const response = await fetch(url, {
            method,
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify(payload),
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error(await response.text());
        }

        const result = await response.json();

        if (result.duplicate) {
            alert('Blog post already exists (duplicate submission prevented). Showing existing post.');
        } else {
            alert(`Blog ${isEditing ? 'updated' : 'created'} successfully!`);
        }

        resetBlogForm();
        loadBlogs(document.getElementById('blogCategoryFilter').value); // Refresh list
        manageTabBtn.click(); // Switch to manage tab

    } catch (error) {
        console.error('[Blog Writer] Submit error:', error);
        alert('Error: ' + error.message);
    } finally {
        // Always re-enable submit button and restore text
        submitBtn.disabled = false;
        submitBtn.textContent = originalButtonText;
    }
}

/**
 * Handle blog migration
 */
async function handleBlogMigration() {
    if (!confirm('This will update all existing blog posts to include the new metadata fields. This is safe but irreversible. Continue?')) {
        return;
    }

    const btn = document.getElementById('migrateBlogsBtn');
    const originalText = btn.textContent;
    btn.textContent = 'Updating...';
    btn.disabled = true;

    try {
        const response = await fetch('/api/blog-writer?migrate=true', {
            credentials: 'include'
        });

        if (!response.ok) {
            throw new Error(`Migration failed: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        alert(`Migration completed successfully! Updated ${result.updated} blog posts.`);

        // Refresh the blog list to show updated data
        loadBlogs(document.getElementById('blogCategoryFilter').value);

    } catch (error) {
        console.error('[Blog Writer] Migration error:', error);
        alert('Migration failed: ' + error.message);
    } finally {
        btn.textContent = originalText;
        btn.disabled = false;
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', initBlogWriterDashboard);
