/**
 * Blog Writer Dash<PERSON> Styles
 * Extracted from pre-bf1328c blogWriter.html
 */

/* ─────────────── LOADING ANIMATION ─────────────── */
@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ─────────────── FORM INPUT STYLES ─────────────── */
.wide-input {
    width: 100%; /* full width of the container */
    padding: 12px 14px; /* taller & roomier */
    font-size: 1.15rem; /* larger text - tweak to taste (1rem ≈ 16px) */
    border: 1px solid #ccc;
    border-radius: 4px;
    box-sizing: border-box; /* keeps the width from overflowing */
}

/* ─────────────── TAB STYLING ─────────────── */
.blog-tabs {
    display: flex;
    margin-bottom: 20px;
    border-bottom: 2px solid #0057B7;
}

.tab-btn {
    padding: 10px 20px;
    background-color: #f0f0f0;
    border: none;
    border-radius: 5px 5px 0 0;
    cursor: pointer;
    margin-right: 5px;
    font-weight: bold;
}

.tab-btn.active {
    background-color: #0057B7;
    color: white;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
    background-color: #f8f9fa;
}

/* ─────────────── IMAGE PREVIEW ─────────────── */
#currentImagePreview img {
    max-width: 150px;
    border-radius: 4px;
    margin-bottom: 10px;
}

/* ─────────────── ENHANCED BLOG FORM ─────────────── */
.enhanced-blog-form {
    max-width: 1000px;
    margin: 0 auto;
}

.form-section {
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    margin-bottom: 25px;
    padding: 20px;
    background-color: #fafafa;
}

.form-section legend {
    font-weight: bold;
    color: #0057B7;
    font-size: 1.1em;
    padding: 0 10px;
    background-color: white;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
}

/* ─────────────── FORM LAYOUT ─────────────── */
.form-row {
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
    margin-bottom: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group.full-width {
    flex: 1 1 100%;
}

.form-group.half-width {
    flex: 1 1 calc(50% - 10px);
}

.form-group.third-width {
    flex: 1 1 calc(33.333% - 14px);
}

.form-group.checkbox-group {
    flex-direction: row;
    align-items: center;
    gap: 10px;
}

.form-group label {
    font-weight: 600;
    margin-bottom: 5px;
    color: #333;
}

.form-group small {
    font-size: 0.85em;
    color: #666;
    margin-top: 3px;
}

/* ─────────────── FORM CONTROLS ─────────────── */
.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
}

.wide-input:focus,
.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #0057B7;
    box-shadow: 0 0 0 3px rgba(0, 87, 183, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 60px;
}

/* ─────────────── CHARACTER COUNTERS ─────────────── */
.char-counter {
    font-size: 0.8em;
    color: #666;
    margin-left: 10px;
    font-weight: normal;
}

/* ─────────────── FORM ACTIONS ─────────────── */
.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e0e0e0;
}

/* ─────────────── BLOG FILTER ─────────────── */
.blog-filter {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.blog-filter label {
    font-weight: 600;
    color: #333;
}

.blog-filter select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
}

/* ─────────────── TABLE STYLES ─────────────── */
.table-container {
    overflow-x: auto;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    background-color: white;
}

.admin-table th,
.admin-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
}

.admin-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #333;
}

.admin-table tbody tr:hover {
    background-color: #f8f9fa;
}

.loading-message {
    text-align: center;
    color: #666;
    font-style: italic;
}

/* ─────────────── ACTION BUTTONS ─────────────── */
.action-btn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2em;
    margin: 0 5px;
    padding: 5px;
    border-radius: 4px;
    transition: background-color 0.2s ease;
}

.action-btn:hover {
    background-color: #f0f0f0;
}

.edit-btn:hover {
    background-color: #e3f2fd;
}

.delete-btn:hover {
    background-color: #ffebee;
}

/* ─────────────── RESPONSIVE DESIGN ─────────────── */
@media (max-width: 768px) {
    .form-group.half-width,
    .form-group.third-width {
        flex: 1 1 100%;
    }
    
    .form-row {
        gap: 10px;
    }
    
    .form-section {
        padding: 15px;
    }
    
    .blog-filter {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }
    
    .form-actions {
        flex-direction: column;
        align-items: stretch;
    }
    
    .admin-table {
        font-size: 0.9em;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 8px 10px;
    }
}
