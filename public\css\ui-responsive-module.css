/* ui-responsive-module.css */
/* This file contains responsive styles for UI elements: contact buttons, hero/CTA banners */
/*
 * CROSS-MODULE DEPENDENCIES:
 * - Extends: styles2.css base responsive patterns
 * - Coordinates with: layout-module.css for container widths
 * - Uses: Same max-width pattern as main content containers
 *
 * LOAD ORDER: Not in standard load order - included separately where needed
 * PURPOSE: Responsive adjustments for specific UI components
 */

/* ====================================================================== */
/*                    CONTACT BUTTON RESPONSIVE STYLES                  */
/* ====================================================================== */

/* Responsive adjustments for contact buttons */
@media (max-width: 768px) {
    .contact-btn {
        padding: 10px 20px;
        font-size: 0.9em;
        min-width: 100px;
    }
}

@media (max-width: 768px) {
    /* List sections with images - stack vertically on tablets */
    .zone-list-row {
        flex-direction: column !important;
        gap: 1.5rem !important;
    }

    .zone-list-row .tutor-box {
        text-align: center !important;
    }

    .zone-list-row div[style*="max-width: 400px"] {
        max-width: 100% !important;
        align-self: center;
    }

    /* Dynamic testimonial sections - stack quotes vertically on tablets */
    .testimonials-bg-section .testimonial-quote-card {
        position: relative !important;
        top: auto !important;
        left: auto !important;
        right: auto !important;
        margin-left: auto !important;
        margin-right: auto !important;
        transform: translateX(77%) !important;
        margin: 1rem auto !important;
        display: block !important;
        max-width: 90% !important;
    }
}

/* Portrait orientation on restricted viewports - testimonials need special handling */
@media (orientation: portrait) and (max-width: 1200px) {
    /* Override existing testimonial positioning rules for restricted viewports */
    .testimonials-laced {
        min-height: auto !important; /* Override the 550px min-height */
        padding: 2rem 1rem !important;
        margin: 0 auto !important;
        text-align: center !important; /* This will center the absolutely positioned children */
    }

    .testimonials-bg-section .testimonial-quote-card,
    .testimonials-laced .testimonial-quote-card:nth-child(1),
    .testimonials-laced .testimonial-quote-card:nth-child(2),
    .testimonials-laced .testimonial-quote-card:nth-child(3),
    .testimonials-laced .card-1,
    .testimonials-laced .card-2,
    .testimonials-laced .card-3 {
        position: relative !important;
        top: auto !important;
        left: auto !important; /* Override styles2.css 900px rule */
        right: auto !important; /* Override styles2.css 900px rule */
        bottom: auto !important;
        transform: translateX(0) !important; /* Override the translateX(77%) from tablet rules */
        margin: 1.5rem auto !important;
        margin-left: auto !important; /* Ensure centering - override 900px rule */
        margin-right: auto !important; /* Ensure centering - override 900px rule */
        display: block !important;
        max-width: 85% !important;
        width: 85% !important;
        box-sizing: border-box !important;
        font-size: 1rem !important; /* Override smaller font sizes */
    }

    /* Ensure testimonial section expands to fit stacked content and is centered */
    .testimonials-bg-section {
        max-width: 1080px !important; /* THE KEY - match other sections' container pattern */
        padding: 2rem 1rem !important;
        min-height: auto !important;
        height: auto !important;
        /* Keep original display behavior - don't use flex for absolutely positioned children */
        display: block !important;
        /* Ensure proper centering in restricted viewports */
        width: 100% !important;
        position: relative !important;
        left: auto !important;
        margin: 0 auto !important;
        /* ADD THESE LINES FOR BETTER CENTERING: */
        text-align: center !important;
        transform: translateX(0) !important; /* Override any inherited transforms */
        /* Ensure background covers the expanded area */
        background-size: cover !important;
        background-position: center !important;
        background-repeat: no-repeat !important;
        box-sizing: border-box !important; /* THE CRUCIAL MISSING PIECE - prevents oversizing */
    }

    /* Fix STRIVE section positioning in restricted portrait viewports */
    .strive-bg-section {
        width: 100% !important;
        position: relative !important;
        left: auto !important;
        right: auto !important;
        margin: 0 auto !important;
        transform: none !important; /* Override any transform rules */
        overflow-x: hidden !important;
        overflow-y: visible !important;
        min-height: auto !important;
        /* Ensure section is visible and properly sized */
        display: block !important;
        height: auto !important;
    }

    /* Ensure STRIVE overlay card is properly positioned */
    .strive-overlay-card {
        max-width: 92vw !important;
        margin: 0 auto !important;
        position: relative !important; /* Ensure it's not absolutely positioned */
        top: auto !important;
        right: auto !important;
        transform: none !important; /* Override any transforms */
    }

    /* Simplified team section for Samsung viewport fix */
    .team-section {
        max-width: 92vw !important;
        margin: 1rem auto !important;
        padding: 1.5rem 1rem !important;
    }
}

@media (max-width: 480px) {
    .contact-btn {
        padding: 8px 16px;
        font-size: 0.85em;
        min-width: 90px;
        display: block;
        margin: 0 auto;
        max-width: 200px;
    }

    .tutor-contact {
        margin: 10px 0;
    }
}

/* ====================================================================== */
/*                    HERO & CTA BANNER RESPONSIVE STYLES              */
/* ====================================================================== */

/* Responsive tweaks for hero and CTA banners */
@media (max-width: 900px) {
    .hero-banner {
        min-height: 300px;
        padding: 2rem 1rem;
    }

    .hero-content {
        padding: 1.5rem;
    }

    .hero-content h1 {
        font-size: 2rem;
    }

    .hero-content p {
        font-size: 1rem;
    }

    .cta-banner {
        min-height: 180px;
        padding: 1.5rem 1rem;
    }
}

/* Portrait-specific adjustments for hero banner */
/* Specificity: 0,0,1,0 + media query - Overrides base hero-banner styles */
@media (max-width: 1200px) and (orientation: portrait) {
    .hero-banner {
        max-width: 1080px; /* Matches layout-module.css container pattern */
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        padding-left: 1rem;
        padding-right: 1rem;
        box-sizing: border-box;
    }
}

/* Portrait-specific adjustments for CTA banner */
@media (max-width: 1200px) and (orientation: portrait) {
    .cta-banner {
        max-width: 1080px;
        width: 100%;
        margin-left: auto;
        margin-right: auto;
        padding-left: 1rem;
        padding-right: 1rem;
        box-sizing: border-box;
    }
}

/* Landscape-specific CTA banner adjustment */
@media (orientation: landscape) {
    .cta-banner {
        transform: translateX(-7px) !important;
    }
}

/* Landscape-specific fixes for restricted viewports */
@media (orientation: landscape) and (max-width: 1200px) {
    /* Prevent STRIVE section cropping in landscape restricted viewports */
    .strive-bg-section {
        width: 100% !important;
        position: relative !important;
        left: auto !important;
        right: auto !important;
        margin: 0 auto !important;
        transform: none !important; /* Override the translateX(10px) that causes cropping */
        overflow-x: hidden !important;
        overflow-y: visible !important;
        min-height: 400px !important; /* Ensure minimum height for landscape */
        height: auto !important;
    }

    /* Ensure STRIVE overlay card fits in landscape */
    .strive-overlay-card {
        max-width: 85vw !important; /* Slightly smaller for landscape */
        margin: 0 auto !important;
        position: relative !important;
        top: auto !important;
        right: auto !important;
        transform: none !important;
        padding: 1.5rem 1rem !important; /* Reduce padding for landscape */
    }

    /* Override any CTA banner transforms that might affect layout */
    .cta-banner {
        transform: none !important; /* Remove the translateX(-7px) if it's causing issues */
    }
}
